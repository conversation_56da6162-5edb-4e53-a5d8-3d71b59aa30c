package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"

	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/gin-gonic/gin"
)

type AssignmentHandler struct {
	assignmentService in.AssignmentService
}

func NewAssignmentHandler(service in.AssignmentService) *AssignmentHandler {
	return &AssignmentHandler{
		assignmentService: service,
	}
}

func (h *AssignmentHandler) GetAllAssignments(c *gin.Context) {
	assignments, err := h.assignmentService.GetAll()
	if err != nil {
		_ = c.Error(err)
		return
	}

	listResponse := dto.NewAssignmentsListResponse(assignments, "Assignments retrieved successfully")
	response.SuccessMany(c, listResponse)
}

func (h *AssignmentHandler) GetByID(c *gin.Context) {

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid user ID format. Must be a positive integer."))
		return
	}
	assignment, err := h.assignmentService.GetByID(uint(id))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainAssignment(*assignment))
}

func (h *AssignmentHandler) GetUserAssignments(c *gin.Context) {
	claims, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	assignments, err := h.assignmentService.GetByUserId(claims.UserID)
	if err != nil {
		_ = c.Error(err)
		return
	}

	listResponse := dto.NewAssignmentsListResponse(assignments, "Assignments retrieved successfully")
	response.SuccessMany(c, listResponse)
}

func (h *AssignmentHandler) CreateAssignment(c *gin.Context) {
	claims, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	var req dto.CreateAssignmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	assignmentToCreate := dto.ToDomainAssignment(&req, claims.UserID)
	createdAssignment, err := h.assignmentService.Create(assignmentToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainAssignment(*createdAssignment))
}

func (h *AssignmentHandler) UpdateAssignment(c *gin.Context) {
	claims, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	assignmentIDStr := c.Param("id")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID", err))
		return
	}

	var req dto.UpdateAssignmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	assignmentToUpdate := dto.ToUpdateDomainAssignment(&req, uint(assignmentID))
	assignmentToUpdate.CreatedByID = claims.UserID

	updatedAssignment, err := h.assignmentService.Update(assignmentToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainAssignment(*updatedAssignment))
}
